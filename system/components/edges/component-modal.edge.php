@props([
    'title' => 'model',
    'description' => 'A model',
    'content' => [], // An array of [('header'|'body'|'footer') => 'content']
    'title' => '',
])

<div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" x-show="showModal" style="display:none;">
    <div class="bg-opacity-75 fixed inset-0 bg-gray-500 transition-opacity" aria-hidden="true"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"></div>
    <div class="w-screen overflow-y-auto fixed inset-0 z-50">
        <div class="min-h-full flex justify-center items-end p-4 text-center sm:items-center sm:p-0">
            <div id="modal"
                 class="transform bg-white rounded-lg shadow-xl transition-all"
                 :class="modalResizable ? 'modal-resizable overflow-auto' : 'max-w-7xl overflow-auto'"
                 :style="modalResizable ? `position: fixed; max-width: none; width: ${modalWidth}px; height: ${modalHeight}px;` : ''"
                 @click.away="showModal = false"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-init="if (modalResizable) { $el.style.left = modalX + 'px'; $el.style.top = modalY + 'px'; }">

                <!-- Modal Header with Drag Handle -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50 rounded-t-lg cursor-move"
                     @mousedown="startDrag($event)"
                     @touchstart="startDrag($event)">
                    <h3 class="text-lg font-medium text-gray-900 select-none">
                        <span x-show="modalTabs.length === 0">Modal</span>
                        <span x-show="modalTabs.length > 0" x-text="`Modal (${modalTabs.length} tab${modalTabs.length !== 1 ? 's' : ''})`"></span>
                    </h3>
                    <div class="flex items-center space-x-2">
                        <!-- Resize Toggle Button -->
                        <button type="button"
                                @click="toggleResize()"
                                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
                                :title="modalResizable ? 'Disable resize' : 'Enable resize'">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      :d="modalResizable ? 'M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4' : 'M4 8h16M4 16h16'"></path>
                            </svg>
                        </button>
                        <!-- Close Button -->
                        <button type="button"
                                @click="showModal = false"
                                class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div x-show="modalTabs.length > 0" class="border-b border-gray-200">
                    <nav class="flex space-x-8 px-4" aria-label="Tabs">
                        <template x-for="(tab, index) in modalTabs" :key="tab.id">
                            <div class="flex items-center">
                                <button :id="`tab-${tab.id}`"
                                        :data-tab-index="index"
                                        @click="setActiveTab(index)"
                                        @keydown="handleTabKeydown($event, index)"
                                        :class="activeTabIndex === index ?
                                            'border-indigo-500 text-indigo-600' :
                                            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                        class="whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm focus:outline-none focus:text-indigo-600 focus:border-indigo-500">
                                    <span x-text="tab.title || `Tab ${index + 1}`"></span>
                                </button>
                                <button x-show="tab.closeable !== false && modalTabs.length > 1"
                                        @click="closeTab(index)"
                                        class="ml-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                        </template>
                    </nav>
                </div>

                <!-- Tab Content -->
                <div class="relative">
                    <!-- Legacy single content (when no tabs) -->
                    <div x-show="modalTabs.length === 0" id="modal_body" class="w-full m-0 text-center sm:mt-0 sm:ml-0 sm:text-left p-4"></div>

                    <!-- Tab panels -->
                    <template x-for="(tab, index) in modalTabs" :key="tab.id">
                        <div :id="`modal_tab_${tab.id}`"
                             x-show="activeTabIndex === index"
                             class="w-full m-0 text-center sm:mt-0 sm:ml-0 sm:text-left p-4"
                             x-html="tab.content">
                        </div>
                    </template>
                </div>

                <!-- Resize Handles (only when resizable) -->
                <template x-if="modalResizable">
                    <div>
                        <!-- Corner resize handles -->
                        <div class="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize bg-gray-300 opacity-50 hover:opacity-75"
                             @mousedown="startResize($event, 'se')"
                             @touchstart="startResize($event, 'se')"></div>
                        <div class="absolute bottom-0 left-0 w-4 h-4 cursor-sw-resize bg-gray-300 opacity-50 hover:opacity-75"
                             @mousedown="startResize($event, 'sw')"
                             @touchstart="startResize($event, 'sw')"></div>
                        <div class="absolute top-0 right-0 w-4 h-4 cursor-ne-resize bg-gray-300 opacity-50 hover:opacity-75"
                             @mousedown="startResize($event, 'ne')"
                             @touchstart="startResize($event, 'ne')"></div>
                        <div class="absolute top-0 left-0 w-4 h-4 cursor-nw-resize bg-gray-300 opacity-50 hover:opacity-75"
                             @mousedown="startResize($event, 'nw')"
                             @touchstart="startResize($event, 'nw')"></div>

                        <!-- Edge resize handles -->
                        <div class="absolute top-0 left-4 right-4 h-2 cursor-n-resize"
                             @mousedown="startResize($event, 'n')"
                             @touchstart="startResize($event, 'n')"></div>
                        <div class="absolute bottom-0 left-4 right-4 h-2 cursor-s-resize"
                             @mousedown="startResize($event, 's')"
                             @touchstart="startResize($event, 's')"></div>
                        <div class="absolute left-0 top-4 bottom-4 w-2 cursor-w-resize"
                             @mousedown="startResize($event, 'w')"
                             @touchstart="startResize($event, 'w')"></div>
                        <div class="absolute right-0 top-4 bottom-4 w-2 cursor-e-resize"
                             @mousedown="startResize($event, 'e')"
                             @touchstart="startResize($event, 'e')"></div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

