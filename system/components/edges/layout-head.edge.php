<!doctype html>
<html class="h-full bg-white">
<head><title>Autobooks</title>
<link rel="stylesheet" href="https://rsms.me/inter/inter.css">
<!--<link rel="stylesheet" href="css/style.css">-->
<link rel="shortcut icon" href="{{ APP_ROOT }}/system/img/favicon.ico" />
<script src="https://cdn.tailwindcss.com"></script>
<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>

<!-- Jodit Editor -->
<link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
<script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>

<script>
    // Define APP_ROOT for JavaScript files
    var APP_ROOT = '<?= APP_ROOT ?>';

    // Navigation tree initialization function
    function initNavTree() {
        return {
            currentRoute: localStorage.getItem('currentNavRoute') || window.location.pathname.replace(APP_ROOT, '').replace(/^\/+|\/+$/g, ''),
            sortableInstances: [],
            init() {
                // Set initial route based on current URL
                if (!localStorage.getItem('currentNavRoute')) {
                    localStorage.setItem('currentNavRoute', this.currentRoute);
                }

                // Initialize sortable functionality after a short delay
                setTimeout(() => this.initSortable(), 100);

                // Listen for HTMX after-request events
                document.body.addEventListener('htmx:afterOnLoad', (event) => {
                    // Don't update if this is a background request (not navigation)
                    if (event.detail.target.id !== 'content_wrapper') return;

                    // Extract the path from the URL
                    const url = new URL(event.detail.xhr.responseURL);
                    const path = url.pathname.replace(APP_ROOT, '');
                    const cleanPath = path.replace(/^\/+|\/+$/g, '');

                    // Update the current route
                    this.currentRoute = cleanPath;
                    localStorage.setItem('currentNavRoute', cleanPath);

                    // Dispatch a custom event to synchronize all nav-tree instances
                    window.dispatchEvent(new CustomEvent('nav-route-changed', { detail: { route: cleanPath } }));
                });

                // Listen for route change events from other nav-tree instances
                window.addEventListener('nav-route-changed', (event) => {
                    this.currentRoute = event.detail.route;
                });

                // Reinitialize sortable after HTMX updates
                document.body.addEventListener('htmx:afterSettle', () => {
                    this.destroySortable();
                    setTimeout(() => this.initSortable(), 100);
                });
            },
            destroySortable() {
                // Destroy existing sortable instances
                this.sortableInstances.forEach(instance => {
                    if (instance && instance.destroy) {
                        instance.destroy();
                    }
                });
                this.sortableInstances = [];
            },
            initSortable() {
                // Only initialize if user has admin/dev role
                if (!document.querySelector('.drag-handle')) return;

                // Find all sortable lists
                const sortableLists = document.querySelectorAll('[data-sortable="true"]');

                sortableLists.forEach(list => {
                    const parentPath = list.dataset.parentPath || 'root';
                    const depth = parseInt(list.dataset.depth || '0');

                    const sortable = Sortable.create(list, {
                        group: {
                            name: 'nav-tree',
                            pull: true,
                            put: true
                        },
                        animation: 150,
                        handle: '.drag-handle',
                        ghostClass: 'sortable-ghost',
                        chosenClass: 'sortable-chosen',
                        dragClass: 'sortable-drag',
                        fallbackOnBody: true,
                        swapThreshold: 0.65,
                        onStart: (evt) => {
                            // Add visual feedback
                            document.body.classList.add('sorting-active');
                            evt.item.classList.add('dragging');
                        },
                        onEnd: (evt) => {
                            // Remove visual feedback
                            document.body.classList.remove('sorting-active');
                            evt.item.classList.remove('dragging');

                            // Handle the drop
                            this.handleSortableEnd(evt, parentPath);
                        },
                        onMove: (evt) => {
                            // Prevent dropping on non-sortable items
                            return evt.related.classList.contains('sortable-item');
                        }
                    });

                    this.sortableInstances.push(sortable);
                });
            },
            handleSortableEnd(evt, originalParentPath) {
                const item = evt.item;
                const newIndex = evt.newIndex;
                const oldIndex = evt.oldIndex;
                const from = evt.from;
                const to = evt.to;

                // Get the actual parent paths from the containers
                const fromParentPath = from.dataset.parentPath || 'root';
                const toParentPath = to.dataset.parentPath || 'root';

                // Check if item was moved to a different list (subfolder creation)
                if (from !== to) {
                    const itemKey = item.dataset.routeKey;

                    // Move to different parent (subfolder)
                    htmx.ajax('POST', APP_ROOT + '/api/system/nav_tree/move_to_subfolder', {
                        values: {
                            item_key: itemKey,
                            target_key: toParentPath.split('/').pop(), // Get the last part of the path
                            current_parent: fromParentPath,
                            target_parent: toParentPath
                        },
                        target: 'body',
                        swap: 'none'
                    }).then(() => {
                        // Refresh the navigation tree after successful move
                        setTimeout(() => window.location.reload(), 500);
                    }).catch(() => {
                        // Revert the move on error
                        from.insertBefore(item, from.children[oldIndex]);
                    });
                } else if (oldIndex !== newIndex) {
                    // Reorder within same parent
                    const items = Array.from(to.querySelectorAll('.sortable-item')).map((el, index) => ({
                        route_key: el.dataset.routeKey,
                        sort_order: index + 1
                    }));

                    htmx.ajax('POST', APP_ROOT + '/api/system/nav_tree/reorder_navigation', {
                        values: {
                            items: JSON.stringify(items),
                            parent_path: toParentPath
                        },
                        target: 'body',
                        swap: 'none'
                    }).catch(() => {
                        // Revert the move on error
                        if (oldIndex < newIndex) {
                            to.insertBefore(item, to.children[oldIndex]);
                        } else {
                            to.insertBefore(item, to.children[oldIndex + 1]);
                        }
                    });
                }
            }
        };
    }
</script>

<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
<script defer src="https://unpkg.com/htmx.org@2.0.3"></script>
<script defer src="https://unpkg.com/htmx-ext-class-tools@2.0.1/class-tools.js"></script>
<script defer src="resources/components/js/notification-handler.js"></script>
<script defer src="<?= APP_ROOT ?>/resources/js/htmx-sse.js"></script>

<SCRIPT>window.Components = {}

    window.Components.listbox = function listbox(options) {
        let modelName = options.modelName || 'selected'
        let pointer = useTrackedPointer()

        return {
            init() {
                this.optionCount = this.$refs.listbox.children.length
                this.$watch('activeIndex', (value) => {
                    if (!this.open) return

                    if (this.activeIndex === null) {
                        this.activeDescendant = ''
                        return
                    }

                    this.activeDescendant = this.$refs.listbox.children[this.activeIndex].id
                })
            },
            activeDescendant: null,
            optionCount: null,
            open: false,
            activeIndex: null,
            selectedIndex: 0,
            get active() {
                return this.items[this.activeIndex]
            },
            get [modelName]() {
                return this.items[this.selectedIndex]
            },
            choose(option) {
                this.selectedIndex = option
                this.open = false
            },
            onButtonClick() {
                if (this.open) return
                this.activeIndex = this.selectedIndex
                this.open = true
                this.$nextTick(() => {
                    this.$refs.listbox.focus()
                    this.$refs.listbox.children[this.activeIndex].scrollIntoView({ block: 'nearest' })
                })
            },
            onOptionSelect() {
                if (this.activeIndex !== null) {
                    this.selectedIndex = this.activeIndex
                }
                this.open = false
                this.$refs.button.focus()
            },
            onEscape() {
                this.open = false
                this.$refs.button.focus()
            },
            onArrowUp() {
                this.activeIndex = this.activeIndex - 1 < 0 ? this.optionCount - 1 : this.activeIndex - 1
                this.$refs.listbox.children[this.activeIndex].scrollIntoView({ block: 'nearest' })
            },
            onArrowDown() {
                this.activeIndex = this.activeIndex + 1 > this.optionCount - 1 ? 0 : this.activeIndex + 1
                this.$refs.listbox.children[this.activeIndex].scrollIntoView({ block: 'nearest' })
            },
            onMouseEnter(evt) {
                pointer.update(evt)
            },
            onMouseMove(evt, newIndex) {
                // Only highlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = newIndex
            },
            onMouseLeave(evt) {
                // Only unhighlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = null
            },
            ...options,
        }
    }

    window.Components.menu = function menu(options = { open: false }) {
        let pointer = useTrackedPointer()

        return {
            init() {
                this.items = Array.from(this.$el.querySelectorAll('[role="menuitem"]'))
                this.$watch('open', () => {
                    if (this.open) {
                        this.activeIndex = -1
                    }
                })
            },
            activeDescendant: null,
            activeIndex: null,
            items: null,
            open: options.open,
            focusButton() {
                this.$refs.button.focus()
            },
            onButtonClick() {
                this.open = !this.open
                if (this.open) {
                    this.$nextTick(() => {
                        this.$refs['menu-items'].focus()
                    })
                }
            },
            onButtonEnter() {
                this.open = !this.open
                if (this.open) {
                    this.activeIndex = 0
                    this.activeDescendant = this.items[this.activeIndex].id
                    this.$nextTick(() => {
                        this.$refs['menu-items'].focus()
                    })
                }
            },
            onArrowUp() {
                if (!this.open) {
                    this.open = true
                    this.activeIndex = this.items.length - 1
                    this.activeDescendant = this.items[this.activeIndex].id

                    return
                }

                if (this.activeIndex === 0) {
                    return
                }

                this.activeIndex = this.activeIndex === -1 ? this.items.length - 1 : this.activeIndex - 1
                this.activeDescendant = this.items[this.activeIndex].id
            },
            onArrowDown() {
                if (!this.open) {
                    this.open = true
                    this.activeIndex = 0
                    this.activeDescendant = this.items[this.activeIndex].id

                    return
                }

                if (this.activeIndex === this.items.length - 1) {
                    return
                }

                this.activeIndex = this.activeIndex + 1
                this.activeDescendant = this.items[this.activeIndex].id
            },
            onClickAway($event) {
                if (this.open) {
                    const focusableSelector = [
                        '[contentEditable=true]',
                        '[tabindex]',
                        'a[href]',
                        'area[href]',
                        'button:not([disabled])',
                        'iframe',
                        'input:not([disabled])',
                        'select:not([disabled])',
                        'textarea:not([disabled])',
                    ]
                        .map((selector) => `${selector}:not([tabindex='-1'])`)
                        .join(',')

                    this.open = false

                    if (!$event.target.closest(focusableSelector)) {
                        this.focusButton()
                    }
                }
            },

            onMouseEnter(evt) {
                pointer.update(evt)
            },
            onMouseMove(evt, newIndex) {
                // Only highlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = newIndex
            },
            onMouseLeave(evt) {
                // Only unhighlight when the cursor has moved
                // Pressing arrow keys can otherwise scroll the container and override the selected item
                if (!pointer.wasMoved(evt)) return
                this.activeIndex = -1
            },
        }
    }

    window.Components.popoverGroup = function popoverGroup() {
        return {
            __type: 'popoverGroup',
            init() {
                let handler = (e) => {
                    if (!document.body.contains(this.$el)) {
                        window.removeEventListener('focus', handler, true)
                        return
                    }
                    if (e.target instanceof Element && !this.$el.contains(e.target)) {
                        window.dispatchEvent(
                            new CustomEvent('close-popover-group', {
                                detail: this.$el,
                            })
                        )
                    }
                }
                window.addEventListener('focus', handler, true)
            },
        }
    }

    window.Components.popover = function popover({ open = false, focus = false } = {}) {
        const focusableSelector = [
            '[contentEditable=true]',
            '[tabindex]',
            'a[href]',
            'area[href]',
            'button:not([disabled])',
            'iframe',
            'input:not([disabled])',
            'select:not([disabled])',
            'textarea:not([disabled])',
        ]
            .map((selector) => `${selector}:not([tabindex='-1'])`)
            .join(',')

        function focusFirst(container) {
            const focusableElements = Array.from(container.querySelectorAll(focusableSelector))

            function tryFocus(element) {
                if (element === undefined) return

                element.focus({ preventScroll: true })

                if (document.activeElement !== element) {
                    tryFocus(focusableElements[focusableElements.indexOf(element) + 1])
                }
            }

            tryFocus(focusableElements[0])
        }

        return {
            __type: 'popover',
            open,
            init() {
                if (focus) {
                    this.$watch('open', (open) => {
                        if (open) {
                            this.$nextTick(() => {
                                focusFirst(this.$refs.panel)
                            })
                        }
                    })
                }

                let handler = (e) => {
                    if (!document.body.contains(this.$el)) {
                        window.removeEventListener('focus', handler, true)
                        return
                    }
                    let ref = focus ? this.$refs.panel : this.$el
                    if (this.open && e.target instanceof Element && !ref.contains(e.target)) {
                        let node = this.$el
                        while (node.parentNode) {
                            node = node.parentNode
                            if (node.__x instanceof this.constructor) {
                                if (node.__x.$data.__type === 'popoverGroup') return
                                if (node.__x.$data.__type === 'popover') break
                            }
                        }
                        this.open = false
                    }
                }

                window.addEventListener('focus', handler, true)
            },
            onEscape() {
                this.open = false
                if (this.restoreEl) {
                    this.restoreEl.focus()
                }
            },
            onClosePopoverGroup(e) {
                if (e.detail.contains(this.$el)) {
                    this.open = false
                }
            },
            toggle(e) {
                this.open = !this.open
                if (this.open) {
                    this.restoreEl = e.currentTarget
                } else if (this.restoreEl) {
                    this.restoreEl.focus()
                }
            },
        }
    }

    window.Components.radioGroup = function radioGroup({ initialCheckedIndex = 0 } = {}) {
        return {
            value: undefined,
            active: undefined,
            init() {
                let options = Array.from(this.$el.querySelectorAll('input'))

                this.value = options[initialCheckedIndex]?.value

                for (let option of options) {
                    option.addEventListener('change', () => {
                        this.active = option.value
                    })
                    option.addEventListener('focus', () => {
                        this.active = option.value
                    })
                }

                window.addEventListener(
                    'focus',
                    () => {
                        if (!options.includes(document.activeElement)) {
                            this.active = undefined
                        }
                    },
                    true
                )
            },
        }
    }

    window.Components.tabs = function tabs() {
        return {
            selectedIndex: 0,
            onTabClick(event) {
                if (!this.$el.contains(event.detail)) return

                let tabs = Array.from(this.$el.querySelectorAll('[x-data^="Components.tab("]'))
                let panels = Array.from(this.$el.querySelectorAll('[x-data^="Components.tabPanel("]'))

                let idx = tabs.indexOf(event.detail)
                this.selectedIndex = idx

                window.dispatchEvent(
                    new CustomEvent('tab-select', {
                        detail: {
                            tab: event.detail,
                            panel: panels[idx],
                        },
                    })
                )
            },
            onTabKeydown(event) {
                if (!this.$el.contains(event.detail.tab)) return

                let tabs = Array.from(this.$el.querySelectorAll('[x-data^="Components.tab("]'))
                let tabIndex = tabs.indexOf(event.detail.tab)

                if (event.detail.key === 'ArrowLeft') {
                    this.onTabClick({ detail: tabs[(tabIndex - 1 + tabs.length) % tabs.length] })
                } else if (event.detail.key === 'ArrowRight') {
                    this.onTabClick({ detail: tabs[(tabIndex + 1) % tabs.length] })
                } else if (event.detail.key === 'Home' || event.detail.key === 'PageUp') {
                    this.onTabClick({ detail: tabs[0] })
                } else if (event.detail.key === 'End' || event.detail.key === 'PageDown') {
                    this.onTabClick({ detail: tabs[tabs.length - 1] })
                }
            },
        }
    }

    window.Components.tab = function tab(defaultIndex = 0) {
        return {
            selected: false,
            init() {
                let tabs = Array.from(
                    this.$el
                        .closest('[x-data^="Components.tabs("]')
                        .querySelectorAll('[x-data^="Components.tab("]')
                )
                this.selected = tabs.indexOf(this.$el) === defaultIndex
                this.$watch('selected', (selected) => {
                    if (selected) {
                        this.$el.focus()
                    }
                })
            },
            onClick() {
                window.dispatchEvent(
                    new CustomEvent('tab-click', {
                        detail: this.$el,
                    })
                )
            },
            onKeydown(event) {
                if (['ArrowLeft', 'ArrowRight', 'Home', 'PageUp', 'End', 'PageDown'].includes(event.key)) {
                    event.preventDefault()
                }

                window.dispatchEvent(
                    new CustomEvent('tab-keydown', {
                        detail: {
                            tab: this.$el,
                            key: event.key,
                        },
                    })
                )
            },
            onTabSelect(event) {
                this.selected = event.detail.tab === this.$el
            },
        }
    }

    window.Components.tabPanel = function tabPanel(defaultIndex = 0) {
        return {
            selected: false,
            init() {
                let panels = Array.from(
                    this.$el
                        .closest('[x-data^="Components.tabs("]')
                        .querySelectorAll('[x-data^="Components.tabPanel("]')
                )
                this.selected = panels.indexOf(this.$el) === defaultIndex
            },
            onTabSelect(event) {
                this.selected = event.detail.panel === this.$el
            },
        }
    }

    function useTrackedPointer() {
        /** @type {[x: number, y: number]} */
        let lastPos = [-1, -1]

        return {
            /**
             * @param {PointerEvent} evt
             */
            wasMoved(evt) {
                let newPos = [evt.screenX, evt.screenY]

                if (lastPos[0] === newPos[0] && lastPos[1] === newPos[1]) {
                    return false
                }

                lastPos = newPos
                return true
            },

            /**
             * @param {PointerEvent} evt
             */
            update(evt) {
                lastPos = [evt.screenX, evt.screenY]
            },
        }
    }

    // Modal Manager with Tab Support and Drag/Resize
    window.modalManager = function() {
        return {
            showModal: false,
            modalTabs: [],
            activeTabIndex: 0,
            modalResizable: false,
            modalX: 0,
            modalY: 0,
            modalWidth: 800,
            modalHeight: 600,
            isDragging: false,
            isResizing: false,
            resizeDirection: '',
            dragStartX: 0,
            dragStartY: 0,
            dragStartModalX: 0,
            dragStartModalY: 0,
            resizeStartX: 0,
            resizeStartY: 0,
            resizeStartWidth: 0,
            resizeStartHeight: 0,

            init() {
                // Center modal initially
                this.centerModal();

                // Listen for window resize to keep modal in bounds
                window.addEventListener('resize', () => {
                    if (this.modalResizable) {
                        this.keepModalInBounds();
                    }
                });

                // Listen for HTMX events to handle tab creation
                const self = this;
                document.body.addEventListener('htmx:beforeSwap', function(event) {
                    const openInTab = event.detail.xhr.getResponseHeader('HX-Open-Tab');
                    const target = event.detail.target;

                    // Check if this is targeting modal_body
                    if (target && target.id === 'modal_body') {
                        // If modal is already open, automatically open in new tab
                        if (self.showModal && !openInTab) {
                            // Prevent the default swap
                            event.preventDefault();

                            // Create a new tab with auto-generated title
                            const title = self.generateTabTitle(event.detail.serverResponse);
                            self.addTab(title, event.detail.serverResponse, null, true);
                            return;
                        }

                        // Handle explicit tab opening
                        if (openInTab) {
                            // Prevent the default swap
                            event.preventDefault();

                            try {
                                const tabData = JSON.parse(openInTab);
                                self.addTab(tabData.title, event.detail.serverResponse, tabData.id, tabData.closeable);

                                // Show modal if not already shown
                                if (!self.showModal) {
                                    self.showModal = true;
                                }
                            } catch (e) {
                                console.error('Error parsing HX-Open-Tab header:', e);
                            }
                            return;
                        }

                        // If modal is not open and no explicit tab request, handle normally
                        // This allows legacy single-content mode when modal is first opened
                    }
                });
            },

            centerModal() {
                this.modalX = Math.max(0, (window.innerWidth - this.modalWidth) / 2);
                this.modalY = Math.max(0, (window.innerHeight - this.modalHeight) / 2);
            },

            toggleResize() {
                this.modalResizable = !this.modalResizable;
                if (this.modalResizable) {
                    this.centerModal();
                } else {
                    // Reset position when disabling resize
                    this.modalX = 0;
                    this.modalY = 0;
                }
            },

            addTab(title, content, id = null, closeable = true) {
                const tabId = id || 'tab_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                const newTab = {
                    id: tabId,
                    title: title || 'New Tab',
                    content: content || '',
                    closeable: closeable
                };

                this.modalTabs.push(newTab);
                this.activeTabIndex = this.modalTabs.length - 1;
                return tabId;
            },

            setActiveTab(index) {
                if (index >= 0 && index < this.modalTabs.length) {
                    this.activeTabIndex = index;
                }
            },



            handleTabKeydown(event, currentIndex) {
                if (['ArrowLeft', 'ArrowRight', 'Home', 'PageUp', 'End', 'PageDown'].includes(event.key)) {
                    event.preventDefault();

                    if (event.key === 'ArrowLeft') {
                        this.setActiveTab((currentIndex - 1 + this.modalTabs.length) % this.modalTabs.length);
                    } else if (event.key === 'ArrowRight') {
                        this.setActiveTab((currentIndex + 1) % this.modalTabs.length);
                    } else if (event.key === 'Home' || event.key === 'PageUp') {
                        this.setActiveTab(0);
                    } else if (event.key === 'End' || event.key === 'PageDown') {
                        this.setActiveTab(this.modalTabs.length - 1);
                    }
                }
            },

            closeTab(index) {
                if (this.modalTabs.length > 1 && index >= 0 && index < this.modalTabs.length) {
                    this.modalTabs.splice(index, 1);

                    // Adjust active tab index
                    if (this.activeTabIndex >= this.modalTabs.length) {
                        this.activeTabIndex = this.modalTabs.length - 1;
                    } else if (this.activeTabIndex > index) {
                        this.activeTabIndex--;
                    }
                }
            },

            updateTabContent(tabId, content) {
                const tab = this.modalTabs.find(t => t.id === tabId);
                if (tab) {
                    tab.content = content;
                }
            },

            generateTabTitle(content) {
                // Try to extract a meaningful title from the content
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = content;

                // Look for common title patterns
                let title = null;

                // Try h1, h2, h3 tags first
                const headings = tempDiv.querySelectorAll('h1, h2, h3');
                if (headings.length > 0) {
                    title = headings[0].textContent.trim();
                }

                // Try data attributes that might contain titles
                if (!title) {
                    const titleElement = tempDiv.querySelector('[data-title], [data-tab-title]');
                    if (titleElement) {
                        title = titleElement.getAttribute('data-title') || titleElement.getAttribute('data-tab-title');
                    }
                }

                // Try to find quote numbers, order numbers, etc.
                if (!title) {
                    const text = tempDiv.textContent;
                    const patterns = [
                        /Quote\s+(Number[:\s]*)?([A-Z0-9-]+)/i,
                        /Order\s+(Number[:\s]*)?([A-Z0-9-]+)/i,
                        /Customer[:\s]+([^,\n]+)/i,
                        /Subscription[:\s]+([A-Z0-9-]+)/i,
                        /Reference[:\s]+([A-Z0-9-]+)/i,
                        /ID[:\s]+([A-Z0-9-]+)/i,
                        /([A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+)/i, // Pattern like Q-2024-001
                    ];

                    for (const pattern of patterns) {
                        const match = text.match(pattern);
                        if (match) {
                            // Use the captured group if available, otherwise the full match
                            title = match[2] || match[1] || match[0];
                            // Clean up the title
                            title = title.trim().replace(/^[:\s]+|[:\s]+$/g, '');
                            if (title.length > 25) {
                                title = title.substring(0, 25) + '...';
                            }
                            break;
                        }
                    }
                }

                // Try to extract from common form labels
                if (!title) {
                    const labels = tempDiv.querySelectorAll('label, .label, [class*="label"]');
                    for (const label of labels) {
                        const labelText = label.textContent.trim();
                        if (labelText && labelText.length > 3 && labelText.length < 30) {
                            title = labelText.replace(/[:\*]+$/, ''); // Remove trailing colons/asterisks
                            break;
                        }
                    }
                }

                // Fallback to generic title with timestamp
                if (!title || title.length > 30) {
                    const now = new Date();
                    title = `Tab ${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}`;
                }

                return title;
            },



            startDrag(event) {
                if (!this.modalResizable) return;

                event.preventDefault();
                this.isDragging = true;

                const clientX = event.clientX || (event.touches && event.touches[0].clientX);
                const clientY = event.clientY || (event.touches && event.touches[0].clientY);

                const startX = this.modalX;
                const startY = this.modalY;
                const dragStartX = clientX;
                const dragStartY = clientY;

                // Get modal element for direct manipulation
                const modalElement = document.getElementById('modal');
                if (!modalElement) return;

                // Add dragging class and disable transitions for smooth dragging
                modalElement.classList.add('dragging');
                modalElement.style.transition = 'none';

                const handleMouseMove = (e) => {
                    if (!this.isDragging) return;

                    const currentX = e.clientX || (e.touches && e.touches[0].clientX);
                    const currentY = e.clientY || (e.touches && e.touches[0].clientY);

                    const newX = startX + (currentX - dragStartX);
                    const newY = startY + (currentY - dragStartY);

                    // Boundary check
                    const maxX = window.innerWidth - this.modalWidth;
                    const maxY = window.innerHeight - this.modalHeight;
                    const boundedX = Math.max(0, Math.min(newX, maxX));
                    const boundedY = Math.max(0, Math.min(newY, maxY));

                    // Direct transform manipulation - no Alpine.js reactivity
                    modalElement.style.transform = `translate(${boundedX}px, ${boundedY}px)`;
                };

                const handleMouseUp = () => {
                    this.isDragging = false;

                    // Get final position from transform
                    const transform = modalElement.style.transform;
                    const matches = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/);
                    if (matches) {
                        this.modalX = parseFloat(matches[1]);
                        this.modalY = parseFloat(matches[2]);
                    }

                    // Remove dragging class and restore transitions
                    modalElement.classList.remove('dragging');
                    modalElement.style.transition = '';

                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                    document.removeEventListener('touchmove', handleMouseMove);
                    document.removeEventListener('touchend', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                document.addEventListener('touchmove', handleMouseMove);
                document.addEventListener('touchend', handleMouseUp);
            },

            startResize(event, direction) {
                if (!this.modalResizable) return;

                event.preventDefault();
                event.stopPropagation();
                this.isResizing = true;

                const clientX = event.clientX || (event.touches && event.touches[0].clientX);
                const clientY = event.clientY || (event.touches && event.touches[0].clientY);

                const startWidth = this.modalWidth;
                const startHeight = this.modalHeight;
                const startX = this.modalX;
                const startY = this.modalY;
                const resizeStartX = clientX;
                const resizeStartY = clientY;

                // Get modal element for direct manipulation
                const modalElement = document.getElementById('modal');
                if (!modalElement) return;

                // Add resizing class and disable transitions
                modalElement.classList.add('resizing');
                modalElement.style.transition = 'none';

                const handleMouseMove = (e) => {
                    if (!this.isResizing) return;

                    const currentX = e.clientX || (e.touches && e.touches[0].clientX);
                    const currentY = e.clientY || (e.touches && e.touches[0].clientY);

                    const deltaX = currentX - resizeStartX;
                    const deltaY = currentY - resizeStartY;

                    // Calculate new dimensions and position
                    const minWidth = 300;
                    const minHeight = 200;

                    let newWidth = startWidth;
                    let newHeight = startHeight;
                    let newX = startX;
                    let newY = startY;

                    // Handle horizontal resizing
                    if (direction.includes('e')) {
                        newWidth = Math.max(minWidth, startWidth + deltaX);
                    } else if (direction.includes('w')) {
                        const proposedWidth = startWidth - deltaX;
                        if (proposedWidth >= minWidth) {
                            newWidth = proposedWidth;
                            newX = startX + deltaX;
                        }
                    }

                    // Handle vertical resizing
                    if (direction.includes('s')) {
                        newHeight = Math.max(minHeight, startHeight + deltaY);
                    } else if (direction.includes('n')) {
                        const proposedHeight = startHeight - deltaY;
                        if (proposedHeight >= minHeight) {
                            newHeight = proposedHeight;
                            newY = startY + deltaY;
                        }
                    }

                    // Boundary constraints
                    const maxX = window.innerWidth - newWidth;
                    const maxY = window.innerHeight - newHeight;
                    newX = Math.max(0, Math.min(newX, maxX));
                    newY = Math.max(0, Math.min(newY, maxY));

                    // Direct style manipulation - no Alpine.js reactivity
                    modalElement.style.width = newWidth + 'px';
                    modalElement.style.height = newHeight + 'px';
                    modalElement.style.transform = `translate(${newX}px, ${newY}px)`;
                };

                const handleMouseUp = () => {
                    this.isResizing = false;

                    // Get final dimensions and position
                    const computedStyle = window.getComputedStyle(modalElement);
                    this.modalWidth = parseInt(computedStyle.width);
                    this.modalHeight = parseInt(computedStyle.height);

                    const transform = modalElement.style.transform;
                    const matches = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/);
                    if (matches) {
                        this.modalX = parseFloat(matches[1]);
                        this.modalY = parseFloat(matches[2]);
                    }

                    // Remove resizing class and restore transitions
                    modalElement.classList.remove('resizing');
                    modalElement.style.transition = '';

                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                    document.removeEventListener('touchmove', handleMouseMove);
                    document.removeEventListener('touchend', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
                document.addEventListener('touchmove', handleMouseMove);
                document.addEventListener('touchend', handleMouseUp);
            },



            keepModalInBounds() {
                const maxX = window.innerWidth - this.modalWidth;
                const maxY = window.innerHeight - this.modalHeight;

                this.modalX = Math.max(0, Math.min(this.modalX, maxX));
                this.modalY = Math.max(0, Math.min(this.modalY, maxY));
            }
        }
    }
</SCRIPT>

<style>
    .htmx-settling-in {
        background-color: yellow;
        transition: background-color 1s ease-in-out;
    }
    .htmx-settling-out {
        background-color: white;
        transition: background-color 1s ease-in-out;
    }

    /* Sortable styles */
    .sortable-ghost {
        opacity: 0.4;
        background: rgba(59, 130, 246, 0.1) !important;
    }

    .sortable-chosen {
        background: rgba(59, 130, 246, 0.2) !important;
    }

    .sortable-drag {
        transform: rotate(5deg);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    .drop-target {
        background: rgba(34, 197, 94, 0.2) !important;
        border: 2px dashed #22c55e !important;
    }

    .sorting-active .drag-handle {
        cursor: grabbing !important;
    }

    .drag-handle:hover {
        opacity: 0.8;
    }

    /* Modal performance optimizations */
    .modal-resizable {
        will-change: transform, width, height;
        backface-visibility: hidden;
        transform: translateZ(0); /* Force hardware acceleration */
        contain: layout style paint; /* CSS containment for better performance */
    }

    .modal-resizable.dragging,
    .modal-resizable.resizing {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        pointer-events: auto; /* Keep pointer events on modal itself */
    }

    .modal-resizable.dragging *,
    .modal-resizable.resizing * {
        pointer-events: none; /* Disable on children during interaction */
        user-select: none;
        -webkit-user-select: none;
    }

    /* Disable transitions during drag/resize for better performance */
    .modal-resizable.dragging,
    .modal-resizable.resizing {
        transition: none !important;
    }

    .modal-resizable.dragging *,
    .modal-resizable.resizing * {
        transition: none !important;
    }

    /* Resize handle improvements */
    .resize-handle {
        position: absolute;
        background: transparent;
        transition: background-color 0.2s ease;
    }

    .resize-handle:hover {
        background-color: rgba(59, 130, 246, 0.2);
    }

    /* Corner handles */
    .resize-se { bottom: 0; right: 0; width: 12px; height: 12px; cursor: se-resize; }
    .resize-sw { bottom: 0; left: 0; width: 12px; height: 12px; cursor: sw-resize; }
    .resize-ne { top: 0; right: 0; width: 12px; height: 12px; cursor: ne-resize; }
    .resize-nw { top: 0; left: 0; width: 12px; height: 12px; cursor: nw-resize; }

    /* Edge handles */
    .resize-n { top: 0; left: 12px; right: 12px; height: 4px; cursor: n-resize; }
    .resize-s { bottom: 0; left: 12px; right: 12px; height: 4px; cursor: s-resize; }
    .resize-w { left: 0; top: 12px; bottom: 12px; width: 4px; cursor: w-resize; }
    .resize-e { right: 0; top: 12px; bottom: 12px; width: 4px; cursor: e-resize; }
        transform: scale(1.1);
        transition: all 0.2s ease;
    }

    .dragging {
        opacity: 0.6;
        transform: rotate(2deg);
    }

    /* Ensure proper z-index for dragged items */
    .sortable-drag {
        z-index: 9999 !important;
    }

    /* Style for nested lists to maintain depth appearance */
    [data-depth="1"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.3);
    }

    [data-depth="2"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.5);
    }

    [data-depth="3"] .sortable-item {
        border-left: 3px solid rgba(99, 102, 241, 0.7);
    }
</style>
<style>
    html {
        font-family: InterVariable, sans-serif !important;
    }
</style>
    <!-- Include Jodit CSS and JS -->
    <link rel="stylesheet" href="https://unpkg.com/jodit@3/build/jodit.min.css"/>
    <script src="https://unpkg.com/jodit@3/build/jodit.min.js"></script>
    </head>
