<?php
namespace api\modal;

/**
 * Open content in a new modal tab
 * This function sets the HX-Open-Tab header to instruct the frontend to open content in a new tab
 */
function open_in_tab($params = []) {
    $title = $params['title'] ?? 'New Tab';
    $id = $params['id'] ?? null;
    $closeable = $params['closeable'] ?? true;
    
    // Set the header to instruct frontend to open in tab
    header('HX-Open-Tab: ' . json_encode([
        'title' => $title,
        'id' => $id,
        'closeable' => $closeable
    ]));
    
    // Return the content that should be loaded in the tab
    return $params['content'] ?? '';
}

/**
 * Helper function to wrap existing API responses to open in tabs
 * This can be used by other API endpoints to easily convert their responses to tab-opening responses
 */
function wrap_in_tab($content, $title = 'New Tab', $id = null, $closeable = true) {
    header('HX-Open-Tab: ' . json_encode([
        'title' => $title,
        'id' => $id,
        'closeable' => $closeable
    ]));
    
    return $content;
}

/**
 * Example endpoint that demonstrates opening content in a tab
 */
function example_tab_content($params = []) {
    $content = '<div class="p-4">
        <h2 class="text-xl font-bold mb-4">Example Tab Content</h2>
        <p class="mb-4">This content was opened in a new tab!</p>
        <button type="button" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-post="' . APP_ROOT . '/api/modal/example_tab_content"
                hx-target="#modal_body"
                hx-vals=\'{"title": "Another Tab", "content": "More content!"}\'>
            Open Another Tab
        </button>
    </div>';
    
    return wrap_in_tab($content, $params['title'] ?? 'Example Tab', $params['id'] ?? null);
}
