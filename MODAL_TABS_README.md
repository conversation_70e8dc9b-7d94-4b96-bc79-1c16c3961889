# Enhanced Modal with Tabs and <PERSON>ag/Resize

This enhancement adds tab support and drag/resize functionality to the existing modal component while maintaining backward compatibility.

## Features

### Tab Interface
- **Multiple Tabs**: Open multiple pieces of content in the same modal
- **Tab Navigation**: Click tab titles to switch between tabs
- **Closeable Tabs**: Close individual tabs (when multiple tabs exist)
- **Server Integration**: Server responses can specify tab opening via headers
- **🚀 Auto-Tab Opening**: Automatically opens content in new tabs when modal is already open

### Drag & Resize
- **Draggable Modal**: Drag the modal by its header when resize mode is enabled
- **Resizable Modal**: Resize using corner and edge handles
- **Boundary Constraints**: <PERSON><PERSON> stays within browser window bounds
- **Toggle Mode**: Switch between normal centered modal and resizable mode

## Usage

### Opening Content in Tabs (Server-side)

#### Method 1: Using HX-Open-Tab Header
```php
// In your API endpoint
function my_api_function($params) {
    $content = '<div>Your content here</div>';
    
    header('HX-Open-Tab: ' . json_encode([
        'title' => 'My Tab Title',
        'id' => 'unique_tab_id',      // Optional
        'closeable' => true           // Optional, defaults to true
    ]));
    
    return $content;
}
```

#### Method 2: Using the Modal API Helper
```php
// Include the modal API
require_once 'system/api/modal.api.php';

function my_api_function($params) {
    $content = '<div>Your content here</div>';
    
    return \api\modal\wrap_in_tab(
        $content, 
        'My Tab Title',     // title
        'unique_tab_id',    // id (optional)
        true                // closeable (optional)
    );
}
```

#### Method 3: Modifying Existing Endpoints
```php
// Add tab support to existing endpoints
function view($p) {
    // Your existing logic...
    $content = generate_content();
    
    // Check if should open in tab
    if (isset($p['open_in_tab']) && $p['open_in_tab']) {
        header('HX-Open-Tab: ' . json_encode([
            'title' => $p['tab_title'] ?? 'Default Title',
            'id' => $p['tab_id'] ?? null,
            'closeable' => true
        ]));
    }
    
    return $content;
}
```

### Opening Content in Tabs (Client-side)

#### HTMX Button to Open in Tab
```html
<button type="button"
        hx-post="/api/my-endpoint"
        hx-target="#modal_body"
        hx-vals='{"open_in_tab": true, "tab_title": "My Tab"}'
        @click="showModal = true">
    Open in Tab
</button>
```

#### Alpine.js Method
```html
<button @click="addTab('Tab Title', '<div>Content</div>', 'tab_id'); showModal = true">
    Open Tab Programmatically
</button>
```

### Drag & Resize Controls

#### Enable/Disable Resize Mode
```html
<button @click="toggleResize()">
    <span x-text="modalResizable ? 'Disable Resize' : 'Enable Resize'"></span>
</button>
```

#### Programmatic Control
```javascript
// Access the modal manager from Alpine.js
// Enable resize mode
this.modalResizable = true;

// Set position and size
this.modalX = 100;
this.modalY = 100;
this.modalWidth = 800;
this.modalHeight = 600;

// Add a tab programmatically
this.addTab('Title', '<div>Content</div>', 'unique_id');

// Close a tab
this.closeTab(0); // Close first tab

// Switch to a tab
this.setActiveTab(1); // Switch to second tab
```

## Backward Compatibility

The enhanced modal maintains full backward compatibility:

- **Legacy Single Content**: Existing code that loads content into `#modal_body` continues to work
- **Existing HTMX Targets**: All existing `hx-target="#modal_body"` attributes work unchanged
- **Alpine.js State**: The `showModal` variable continues to work as before

## Technical Details

### Alpine.js Data Structure
```javascript
{
    showModal: false,           // Controls modal visibility
    modalTabs: [],             // Array of tab objects
    activeTabIndex: 0,         // Currently active tab index
    modalResizable: false,     // Whether resize mode is enabled
    modalX: 0,                 // Modal X position (when resizable)
    modalY: 0,                 // Modal Y position (when resizable)
    modalWidth: 800,           // Modal width (when resizable)
    modalHeight: 600,          // Modal height (when resizable)
    // ... drag/resize state variables
}
```

### Tab Object Structure
```javascript
{
    id: 'unique_tab_id',       // Unique identifier
    title: 'Tab Title',        // Display title
    content: '<div>...</div>', // HTML content
    closeable: true            // Whether tab can be closed
}
```

### CSS Classes Added
- Resize handles with appropriate cursor styles
- Tab navigation styling
- Drag handle cursor changes

## Testing

Use the test page `test_modal_tabs.php` to test all functionality:

1. Open the test page in your browser
2. Try opening different types of content in tabs
3. Test drag and resize functionality
4. Verify backward compatibility with legacy modal usage

## Integration Examples

### Quotes System
The quotes system has been updated to demonstrate tab usage:
- Regular "View" button opens in legacy modal
- New "Tab" button opens the same content in a new tab

### Custom API Endpoints
Create custom endpoints that leverage the tab system:
```php
function custom_dashboard($params) {
    $content = Edge::render('my-dashboard-template', $params);
    
    return \api\modal\wrap_in_tab(
        $content,
        'Dashboard - ' . $params['user_name'],
        'dashboard_' . $params['user_id']
    );
}
```
