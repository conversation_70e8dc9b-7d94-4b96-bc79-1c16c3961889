<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Modal</title>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .modal { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 50; }
        .modal-content { background: white; margin: 50px auto; padding: 20px; width: 80%; max-width: 600px; border-radius: 8px; }
        .tab-nav { border-bottom: 1px solid #ccc; margin-bottom: 20px; }
        .tab-button { padding: 10px 20px; border: none; background: none; cursor: pointer; border-bottom: 2px solid transparent; }
        .tab-button.active { border-bottom-color: #007bff; color: #007bff; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
    </style>
</head>
<body x-data="modalManager()">
    <h1>Debug Modal Test</h1>
    
    <div>
        <button class="btn btn-primary" @click="showModal = true">Open Modal</button>
        <button class="btn btn-secondary" @click="addTab('Test Tab', '<p>Test content</p>'); showModal = true">Add Tab</button>
        <button class="btn btn-secondary" @click="toggleResize()">Toggle Resize</button>
    </div>
    
    <div>
        <p>Modal Visible: <span x-text="showModal"></span></p>
        <p>Tab Count: <span x-text="modalTabs.length"></span></p>
        <p>Resizable: <span x-text="modalResizable"></span></p>
    </div>

    <!-- Modal -->
    <div x-show="showModal" class="modal" @click.away="showModal = false">
        <div class="modal-content" 
             :style="modalResizable ? `width: ${modalWidth}px; height: ${modalHeight}px; position: fixed; left: ${modalX}px; top: ${modalY}px;` : ''">
            
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h2>Modal Title</h2>
                <button @click="showModal = false">×</button>
            </div>
            
            <!-- Tab Navigation -->
            <div x-show="modalTabs.length > 0" class="tab-nav">
                <template x-for="(tab, index) in modalTabs" :key="tab.id">
                    <button class="tab-button" 
                            :class="activeTabIndex === index ? 'active' : ''"
                            @click="setActiveTab(index)"
                            x-text="tab.title">
                    </button>
                </template>
            </div>
            
            <!-- Content -->
            <div x-show="modalTabs.length === 0">
                <p>No tabs - legacy content would go here</p>
            </div>
            
            <template x-for="(tab, index) in modalTabs" :key="tab.id">
                <div x-show="activeTabIndex === index" x-html="tab.content"></div>
            </template>
        </div>
    </div>

    <script>
        function modalManager() {
            return {
                showModal: false,
                modalTabs: [],
                activeTabIndex: 0,
                modalResizable: false,
                modalX: 100,
                modalY: 100,
                modalWidth: 600,
                modalHeight: 400,

                addTab(title, content, id = null, closeable = true) {
                    const tabId = id || 'tab_' + Date.now();
                    this.modalTabs.push({
                        id: tabId,
                        title: title,
                        content: content,
                        closeable: closeable
                    });
                    this.activeTabIndex = this.modalTabs.length - 1;
                    return tabId;
                },

                setActiveTab(index) {
                    if (index >= 0 && index < this.modalTabs.length) {
                        this.activeTabIndex = index;
                    }
                },

                toggleResize() {
                    this.modalResizable = !this.modalResizable;
                }
            }
        }
    </script>
</body>
</html>
