<?php
namespace api\quotes;

use autodesk_api\autodesk_api;
use autodesk_api\autodesk_quotes;
use Edge\Edge;
use data_table\data_table;
use system\data_importer;
use DateTime;

//print_rr($input_params);


function import_csv_into_database(){
    autodesk_api::import_csv_into_database(autodesk_quotes::get_quote_column_mapping(), DIR_FS_CATALOG . DIRECTORY_SEPARATOR . "feeds/quotes.csv");
}

function search($p){
    return generate_quote_table(criteria: ["search" => $p['search_terms'], "limit" => 50], just_body: false);
}


function data_table_filter($p){
    $criteria = data_table::api_process_criteria($p);
    return generate_quote_table(criteria: $criteria);
}

function create_quote($p){
    $autodesk = new autodesk_api();
    $quote = $autodesk->quote->create_renewal($p['subs_quoteId'], $p['user_id']);
    print_rr($quote,'quoty1');
    $quote_assoc = json_decode($quote, true);
    print_rr($quote_assoc,'quoty2');
    return edge::render('quote-v3-ui-form', ['quote_data' => $quote_assoc]);
}

function send_quote($p){
    $autodesk = new autodesk_api();
    $quotedata = $p['quote'];
    $quote = $autodesk->quote->send_to_autodesk($quotedata);
    print_rr(i:['quote' => $quote,'p' => $p],co:false,full:true);
    return edge::render('layout-card', ['content' =>  $quote]);
}

function email_send_reminder(){
    $autodesk = new autodesk_api();
    $subs = $autodesk->quotes->get_renewable();
    $file_path = FS_APP_ROOT . DIRECTORY_SEPARATOR . 'resources/views/quotes/email_history/reminder_email.view.php';
    $email_template = file_get_contents($file_path);
    if ($email_template === false) {
        throw new Exception("Failed to load email template from {$file_path}");
    }

    // Split the content by lines
    $lines = explode("\n", $email_template);

    $email_rules = explode(',', autodesk_api::database_get_storage('quote_renew_email_send_rules'));
    $settings_days = autodesk_api::database_get_storage('quote_renew_email_send_days');
    $settings_time = autodesk_api::database_get_storage('quote_renew_email_send_time');


    // Check if it's time to send an email.
    foreach ($subs as $sub) {
        if ($subs['tcs_unsubscribe'] = 1) continue;
        $now = new DateTime();
        $end_date = new DateTime($sub['endDate']);
        $date_last_sent = $sub['last_email_sent_date'] ?? $sub['startDate'];
        $last_sent = new DateTime($date_last_sent);
        $days_remaining = $now->diff($end_date)->days;
        $days_since_last_sent = $now->diff($last_sent)->days;
        echo "Processing quote " . $sub['quoteReferenceNumber'] . " id " . $sub['id'] . " for customer: " . $sub['endCustomer_name'] . " End date is " . $sub['endDate'] . " with d/r: {$days_remaining} and last sent: " . $sub['last_email_sent_date'] . ": ";
        foreach ($email_rules as $key => $rule) {
            if ($days_remaining <= $rule) {
                echo " is below rule $rule.<br> " . PHP_EOL;
                if ((($days_remaining + $days_since_last_sent) > $rule)) {
                    echo "sending email <br>" . PHP_EOL;
                    $autodesk->quotes->send_reminder_email($sub['id']);
                    //print_rr($autodesk->quotes->send_reminder_email($sub['id'], $rule));
                }
                break;
            }
        }
    }
}

function view($p) {
    $autodesk = new autodesk_api();

    // Check if this should open in a new tab
    $open_in_tab = isset($p['open_in_tab']) && $p['open_in_tab'];
    $tab_title = $p['tab_title'] ?? null;

    // Define the quote display layout structure
    $quote_display = [
        'quote_details' => [
            'label' => 'Quote Details',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-2',
            'content' => [
                'basic_info' => [
                    'label' => 'Quote Information',
                    'type' => 'layout-card',
                    'fields' => [
                        'quotes_quote_number' => 'Quote Number',
                        'quotes_quote_status' => 'Status',
                        'quotes_quote_created_time' => 'Created',
                        'quotes_quote_expiration_date' => 'Expires',
                        'quotes_quoted_date' => 'Quoted Date',
                        'quotes_quote_opportunity_number' => 'Opportunity Number',
                        'quotes_quote_language' => 'Language'
                    ]
                ]
            ]
        ],
        'customer_info' => [
            'label' => 'Customer Information',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-1',
            'content' => [
                'customer_details' => [
                    'label' => 'Customer Details',
                    'type' => 'layout-card',
                    'fields' => [
                        'endcust_name' => 'Customer Name',
                        'endcust_primary_admin_email' => 'Email',
                        'quotecontact_name' => 'Quote Contact',
                        'quotecontact_email' => 'Contact Email'
                    ]
                ]
            ]
        ],
        'pricing_info' => [
            'label' => 'Pricing Information',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-2',
            'content' => [
                'pricing_details' => [
                    'label' => 'Pricing Details',
                    'type' => 'layout-card',
                    'fields' => [
                        'quotes_quote_currency' => 'Currency',
                        'quotes_total_list_amount' => 'List Amount',
                        'quotes_total_net_amount' => 'Net Amount',
                        'quotes_total_amount' => 'Total Amount',
                        'quotes_total_discount' => 'Discount',
                        'quotes_estimated_tax' => 'Estimated Tax',
                        'quotes_payment_terms_code' => 'Payment Terms',
                        'quotes_payment_terms_description' => 'Payment Description'
                    ]
                ]
            ]
        ],
        'line_items' => [
            'label' => 'Line Items',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'col-span-3',
            'content' => [
                'items' => [
                    'label' => '',
                    'type' => 'function',
                    'content' => function() use ($p) {
                        return generate_quote_line_items_table($p);
                    }
                ]
            ]
        ],
        'timeline' => [
            'label' => 'Timeline',
            'collapsed' => 'false',
            'type' => 'layout-card',
            'class_suffix' => 'row-span-4',
            'content' => [
                'timeline' => [
                    'label' => 'Timeline',
                    'collapsed' => 'false',
                    'type' => 'component-activity-feed',
                    'content' => 'timeline_data'
                ]
            ]
        ]
    ];

    // Get quote data using the existing method
    $criteria = [];
    if (isset($p['quote_number'])) {
        $criteria['where'] = ['quotes.quote_number' => ['=', $p['quote_number']]];
    } elseif (isset($p['id'])) {
        $criteria['where'] = ['quotes.id' => ['=', $p['id']]];
    } else {
        echo Edge::render('layout-card', ['content' => 'No quote identifier provided']);
        return;
    }

    $columns = Edge::parse_tables($quote_display);
    $quotes = $autodesk->quotes->get_all($columns, $criteria);

    if (!$quotes || count($quotes) == 0) {
        echo Edge::render('layout-card', ['content' => 'Quote not found']);
        return;
    }

    $quote = $quotes[0]; // Get the first (and should be only) result

    // Convert the quote data into the format expected by layout-modal_data_display
    $processed_layout = [];
    foreach ($quote_display as $section_key => $section) {
        $processed_section = $section;

        if (isset($section['content'])) {
            foreach ($section['content'] as $content_key => $content) {
                if (isset($content['fields'])) {
                    // Convert fields to the expected format
                    $field_content = [];
                    foreach ($content['fields'] as $field_key => $field_label) {
                        $field_content[] = [
                            'label' => $field_label,
                            'value' => $quote[$field_key] ?? '-'
                        ];
                    }
                    $processed_section['content'][$content_key]['content'] = $field_content;
                    unset($processed_section['content'][$content_key]['fields']);
                }
            }
        }

        $processed_layout[$section_key] = $processed_section;
    }

    // Get activity/timeline data (if available)
    $activity = [];
    // You can add quote history retrieval here if needed

    $out = Edge::render('layout-modal_data_display', [
        'layout' => $processed_layout,
        'content' => [
            'timeline_data' => [
                'activity' => $activity
            ]
        ]
    ]);

    // If opening in tab, set the appropriate header
    if ($open_in_tab) {
        $quote_number = $quote['quotes_quote_number'] ?? 'Unknown';
        $title = $tab_title ?? "Quote {$quote_number}";

        header('HX-Open-Tab: ' . json_encode([
            'title' => $title,
            'id' => 'quote_' . ($quote['quotes_id'] ?? time()),
            'closeable' => true
        ]));
    }

    echo $out;
}

function flag_modal($p):string {
    return Edge::render('component-add-flag', $p);
}

function add_flag($p):void {
    $data = [
        'id' => $p['sub_id'],
        'quoteReferenceNumber' => $p['sub_num'],
        'media' => $p['label'],
        'user_id' => $p['user_id'],
        'message' => $p['message']
    ];
    $autodesk = new autodesk_api();
    $history = $autodesk->insert_history_item($data);
}

