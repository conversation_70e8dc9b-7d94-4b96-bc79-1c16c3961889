<?php

use autodesk_api\autodesk_api;
use edge\Edge;
use data_table\data_table;
//function generate_quote_table(){
//    $autodesk = new autodesk_api();
//    $quotes = $autodesk->quotes->get_current();
//
//
//
//    return generate_quote_table_html($quotes);
//}

function generate_quote_table($criteria = ['limit' =>  30],$just_body = false, $just_rows = false, $htmx_oob = false): false|string {
    print_rr($criteria,'critty');
    $table_structure = [
        'table_id' => 'quotes',
        'db_table' => 'quotes',
        'columns' => [
            ["label" => "Customer", "field" => ['endcust_name','quotecontact_name','quotecontact_email']],
            ["label" => "End Customer", "field" => ['endcust_name','endcust_primary_admin_email']],
            ["label" => "id's", "field" => ['quotes_quote_number','quotes_quote_opportunity_number']],
            ["label" => "Quote Status", "field" => 'quotes_quote_status'],
            ["label" => "Dates", "field" => ['quotes_modified_at','quotes_quote_created_time','quotes_quote_expiration_date']],
            ["label" => "total", "field" => 'quotes_total_amount'],
            ["label" => "Actions", "content" => function ($item) {
                    // Debug: Log the item data to see what fields are available
                    print_rr($item, 'quote_table_item_data');

                    $buttons_html = Edge::render('forms-button', [
                        'type' => 'button',
                        'label' => 'View ',
                        'icon' => 'book-open',
                        'icon_position' => 'replace',
                        'variant' => 'rounded-primary',
                        'hx-post' => APP_ROOT . '/api/view',
                        'hx-swap' => 'innerHTML',
                        'hx-target' => '#modal_body',
                        'hx-vals' => [
                            'quote_number' => $item['quotes_quote_number'],
                            'id' => $item['quotes_id'] ?? $item['id']
                        ],
                        "@click" => "showModal = true",
                        'data-toggle' => "modal",
                        'data-target' => "#quotes_modal",
                        'data-quote-number' => $item['quotes_quote_number'],
                        'title' => 'View quote (opens in new tab if modal is already open)',
                    ]);

                    // Add button to open in new tab
                    $buttons_html .= Edge::render('forms-button', [
                        'type' => 'button',
                        'label' => 'Tab',
                        'icon' => 'plus',
                        'icon_position' => 'replace',
                        'variant' => 'rounded-secondary',
                        'hx-post' => APP_ROOT . '/api/quotes/view',
                        'hx-swap' => 'innerHTML',
                        'hx-target' => '#modal_body',
                        'hx-vals' => [
                            'quote_number' => $item['quotes_quote_number'],
                            'id' => $item['quotes_id'] ?? $item['id'],
                            'open_in_tab' => true,
                            'tab_title' => 'Quote ' . $item['quotes_quote_number']
                        ],
                        "@click" => "showModal = true",
                        'title' => 'Open in new tab',
                    ]);

                    switch ($item['quotes_quote_status']) {
                        case 'Not sent to Autodesk':
                        case 'Not Sent':
                        case 'Failed':
                            $buttons_html .= Edge::render('forms-button', [
                                'type' => 'button',
                                'label' => 'Send to Autodesk',
                                'icon' => 'autodesk',
                                'icon_position' => 'replace',
                                'variant' => 'rounded-primary',
                                'hx-post' => APP_ROOT . '/api/autodesk_send_quote',
                                'hx-vals' => [
                                    "orders_id" => $item['orders_orders_id']
                                ]
                            ]);
                            break;
                        case 'Draft':
                            $buttons_html .= Edge::render('forms-button', [
                                'type' => 'button',
                                'icon' => 'autodesk-finalize',
                                'icon_position' => 'replace',
                                'label' => 'Finalize',
                                'variant' => 'rounded-primary',
                                'hx-post' => APP_ROOT . '/api/autodesk_finalize_quote',
                                'hx-swap' => 'none',
                                'hx-vals' => [
                                    "quote_number" => $item['quotes_quoteNumber']
                                ]
                            ]);
                            break;
                    }
                    return $buttons_html;
            }]
        ]
    ];
    $default_criteria = [];
    $db_data = filter_db_schema($table_structure);
    $default_criteria = [
        "order_by" => "quotes_modified_at DESC",
        "search_columns" => [
            "quotes.quote_number",
            "quotes.quote_status",
            "endcust.account_csn",
            "endcust.name",
            "endcust.address1",
            "endcust.address2",
            "endcust.address3",
            "endcust.city",
            "endcust.state_province",
            "endcust.postal_code",
            "endcust.country",
            "endcust.primary_admin_first_name",
            "endcust.primary_admin_last_name",
            "endcust.primary_admin_email",
            "endcust.team_id",
            "endcust.team_name",
            "endcust.first_name",
            "endcust.last_name",
            "endcust.email"
        ],
        "limit" => 30,
    ];
    $criteria = array_merge($default_criteria, $criteria);
    $autodesk = new autodesk_api();
    $data = $autodesk->quotes->get_all($db_data ,$criteria);

    $filter_criteria = ['limit' => 100];
    foreach ($table_structure['columns'] as $key => $col) {
        if(!isset($col['auto_filter'])) continue;
        $table_structure['columns'][$key]['filter_data'] = $autodesk->subscriptions->get_distinct([$col['field']], $filter_criteria);
    }

    return data_table::process_data_table(
       table_structure: $table_structure,
        data: $data,
        callback: __FUNCTION__,
        criteria: $criteria
    );
}

function generate_quote_line_items_table($p) {
    $autodesk = new autodesk_api();
    $quote_identifier = $p['quote_number'] ?? $p['id'] ?? null;

    if (!$quote_identifier) {
        return '<div class="p-4 text-red-600">No quote identifier provided</div>';
    }

    // Get the quote ID from the quote number if needed
    $quote_id = null;
    if (isset($p['quote_number'])) {
        $quote_query = tcs_db_query("SELECT id FROM autodesk_quotes WHERE quote_number = :quote_number",
            [':quote_number' => $p['quote_number']]);
        if ($quote_query && count($quote_query) > 0) {
            $quote_id = $quote_query[0]['id'];
        }
    } else if (isset($p['id'])) {
        $quote_id = $p['id'];
    }

    if (!$quote_id) {
        return '<div class="p-4 text-red-600">Could not find quote ID</div>';
    }

    // Get line items for this quote
    $line_items = tcs_db_query("
        SELECT * FROM autodesk_quote_line_items
        WHERE quote_id = :quote_id
        ORDER BY line_number ASC",
        [':quote_id' => $quote_id]
    );

    if (!$line_items || count($line_items) == 0) {
        return '<div class="p-4 text-gray-500">No line items found for this quote</div>';
    }

    // Generate the table HTML
    $html = '<div class="overflow-x-auto">';
    $html .= '<table class="min-w-full divide-y divide-gray-200">';
    $html .= '<thead class="bg-gray-50">';
    $html .= '<tr>';

    // Define table columns
    $columns = [
        'line_number' => 'Line #',
        'offering_name' => 'Product',
        'offering_code' => 'Code',
        'action' => 'Action',
        'quantity' => 'Qty',
        'start_date' => 'Start Date',
        'end_date' => 'End Date',
        'unit_srp' => 'Unit Price',
        'extended_srp' => 'Extended Price',
        'end_user_price' => 'Final Price'
    ];

    foreach ($columns as $column_key => $column_label) {
        $html .= '<th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">';
        $html .= htmlspecialchars($column_label);
        $html .= '</th>';
    }

    $html .= '</tr>';
    $html .= '</thead>';
    $html .= '<tbody class="bg-white divide-y divide-gray-200">';

    foreach ($line_items as $item) {
        $html .= '<tr class="hover:bg-gray-50">';

        foreach ($columns as $column_key => $column_label) {
            $html .= '<td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">';

            // Format the value based on column type
            if ($column_key == 'start_date' || $column_key == 'end_date') {
                $value = !empty($item[$column_key]) ? date('M j, Y', strtotime($item[$column_key])) : '-';
            } else if ($column_key == 'unit_srp' || $column_key == 'extended_srp' || $column_key == 'end_user_price') {
                $currency = $item['currency'] ?? 'GBP';
                $value = !empty($item[$column_key]) ? $currency . ' ' . number_format($item[$column_key], 2) : '-';
            } else {
                $value = $item[$column_key] ?? '-';
            }

            $html .= htmlspecialchars($value);
            $html .= '</td>';
        }

        $html .= '</tr>';
    }

    $html .= '</tbody>';
    $html .= '</table>';
    $html .= '</div>';

    return $html;
}
//    $db_data = Edge::filter_db_schema($table_data);
//    $default_criteria = [
//        "order_by" => "quotes.id DESC",
//        "limit" => 30,
//    ];
//    $criteria = array_merge($default_criteria, $criteria);
//    $autodesk = new autodesk_api();
//    $quotes = $autodesk->quotes->get_all($db_data ,$criteria);
//    // build_column_filters
//
//    /*foreach ( $table_data['columns'] as $key => $col ) {
//        if ( isset( $col['auto_filter'] ) ) {
//            $filter_criteria = [
//                "order_by" => $col['field'],
//                'limit' => $col['filter_limit'] ?? 10
//            ];
//            $filter_criteria = array_merge($criteria, $filter_criteria);
//            $table_data['columns'][$key]['filter_data'] = $autodesk->quotes->get_distinct([$col['field']], $filter_criteria);
//        }
//    }*/
//
//
//    // Define the two dates
//    $reminder_date ="";
//    if (isset($items['mailhist_date_sent'])) {
//        $reminder_date = "{$items['mailhist_date_sent']}<br>Rule: {$items['mailhist_date_sent']} days";
//    }
//
//    foreach ($cols_hidden as $col) {
//        unset($row_content[$col]);
//    }
//    $rows = '';
//    $columns = ($just_rows || $just_body) ? Edge::build_column_filters($table_data['columns'], $criteria) : $table_data['columns'];
//    $data = [
//        "title" => "Quotes",
//        "description" => "",
//        "items" => $quotes,
//        "columns" => $columns,
//        "rows" => [
//            'id_prefix' => 'quote_id_',
//            'id_field'=> 'quotes_quote_id'
//        ],
//        "just_body" => $just_body,
//        "just_rows" => $just_rows
//    ];
//    $htmx_oob_out = [];
//    if ($htmx_oob) {
//        $just_rows = true;
//        $data['rows']['extra_parameters'] = 'hx-swap-oob="true" hx-ext="class-tools" classes="add htmx-settling-in, add htmx-settling-out:2s,remove htmx-settling-in:3s, remove htmx-settling-out"';
//        $data['rows']['class-postfix'] = '';
//    }
//
//    $input = '';
//    if (!$just_rows && !$just_body) {
//        $input = Edge::render('forms-input', [
//            'type' => 'hidden',
//            'name' => 'last_update',
//            'value' => date('Y-m-d H:i:s'),
//            'hx-post' => APP_ROOT . '/api/update',
//            'hx-swap'=>'outerHTML',
//            'hx-trigger' => 'every 10s'
//        ]);
//    }
//    return $input . Edge::render('data-table', $data);
