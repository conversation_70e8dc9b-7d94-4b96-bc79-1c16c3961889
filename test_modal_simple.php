<?php
require_once 'system/bootstrap.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Modal Test</title>
    <x-layout-head />
</head>
<body class="h-full bg-gray-100" x-data="modalManager()" x-bind:class="showModal && 'overflow-hidden'">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Simple Modal Test</h1>
        
        <div class="space-y-4">
            <button type="button"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    @click="showModal = true; console.log('Modal state:', {showModal, modalTabs: modalTabs.length, modalResizable})">
                Open Modal (Debug)
            </button>

            <button type="button"
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                    @click="addTab('Test Tab', '<div class=\'p-4\'><h3>Test Content</h3><p>This is a test tab!</p></div>'); showModal = true">
                Add Tab & Open Modal
            </button>

            <button type="button"
                    class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
                    @click="toggleResize(); if (!showModal) showModal = true; console.log('Resize toggled:', modalResizable)">
                Toggle Resize & Open
            </button>

            <button type="button"
                    class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded"
                    @click="addTab('Heavy Content', '<div class=\'p-4\'><h3>Performance Test</h3><p>This tab contains more content to test performance.</p><div style=\'height: 300px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center;\'><p>Drag and resize should be smooth!</p></div></div>'); showModal = true; toggleResize();">
                Performance Test
            </button>

            <!-- Test auto-tab opening -->
            <button type="button"
                    class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                    hx-post="<?= APP_ROOT ?>/api/modal/example_tab_content"
                    hx-target="#modal_body"
                    hx-vals='{"title": "Auto Tab Test", "content": "<div class=\"p-4\"><h2>Auto Tab Test</h2><p>This content was automatically opened in a new tab because the modal was already open!</p><p>Try clicking this button multiple times to see multiple tabs created.</p></div>"}'
                    @click="showModal = true">
                Test Auto-Tab (HTMX)
            </button>
            
            <div class="bg-white p-4 rounded shadow">
                <h3 class="font-bold mb-2">Debug Info:</h3>
                <p>Modal Visible: <span x-text="showModal"></span></p>
                <p>Tab Count: <span x-text="modalTabs.length"></span></p>
                <p>Active Tab: <span x-text="activeTabIndex"></span></p>
                <p>Resizable: <span x-text="modalResizable"></span></p>
                <p>Position: <span x-text="`${modalX}, ${modalY}`"></span></p>
                <p>Size: <span x-text="`${modalWidth}x${modalHeight}`"></span></p>
            </div>

            <div class="bg-blue-50 p-4 rounded shadow mt-4">
                <h3 class="font-bold mb-2 text-blue-800">🚀 Auto-Tab Feature:</h3>
                <div class="text-blue-700 space-y-2">
                    <p><strong>How it works:</strong></p>
                    <ol class="list-decimal list-inside space-y-1 ml-4">
                        <li>Open the modal first (using any button)</li>
                        <li>Then click "Test Auto-Tab (HTMX)" - it will automatically open in a new tab!</li>
                        <li>Click it multiple times to see multiple tabs created</li>
                        <li>This works with any HTMX request targeting #modal_body when modal is open</li>
                    </ol>
                    <p class="mt-3"><strong>Benefits:</strong> Better UX - users can compare multiple items side-by-side without losing their current view!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the modal component -->
    <x-component-modal />
    
    <script>
        // Debug Alpine.js
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized');
        });
        
        // Check if modalManager is available
        setTimeout(() => {
            console.log('modalManager function:', typeof window.modalManager);
        }, 1000);
    </script>
</body>
</html>
