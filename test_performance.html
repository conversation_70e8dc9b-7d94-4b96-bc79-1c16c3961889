<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Performance Test</title>
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f0f0f0; }
        .modal-overlay { position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 50; }
        
        .modal-resizable {
            will-change: transform, width, height;
            backface-visibility: hidden;
            transform: translateZ(0);
            contain: layout style paint;
        }
        
        .modal-resizable.dragging,
        .modal-resizable.resizing {
            user-select: none;
            transition: none !important;
        }
        
        .modal-resizable.dragging *,
        .modal-resizable.resizing * {
            pointer-events: none;
            user-select: none;
            transition: none !important;
        }
        
        .resize-handle { position: absolute; background: transparent; }
        .resize-handle:hover { background-color: rgba(59, 130, 246, 0.2); }
        
        .resize-se { bottom: 0; right: 0; width: 12px; height: 12px; cursor: se-resize; }
        .resize-sw { bottom: 0; left: 0; width: 12px; height: 12px; cursor: sw-resize; }
        .resize-ne { top: 0; right: 0; width: 12px; height: 12px; cursor: ne-resize; }
        .resize-nw { top: 0; left: 0; width: 12px; height: 12px; cursor: nw-resize; }
        .resize-n { top: 0; left: 12px; right: 12px; height: 4px; cursor: n-resize; }
        .resize-s { bottom: 0; left: 12px; right: 12px; height: 4px; cursor: s-resize; }
        .resize-w { left: 0; top: 12px; bottom: 12px; width: 4px; cursor: w-resize; }
        .resize-e { right: 0; top: 12px; bottom: 12px; width: 4px; cursor: e-resize; }
        
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .performance-info { background: white; padding: 15px; border-radius: 8px; margin: 10px 0; }
    </style>
</head>
<body x-data="performanceTest()">
    <h1>High-Performance Modal Test</h1>
    
    <div class="performance-info">
        <h3>Performance Optimizations:</h3>
        <ul>
            <li>✅ CSS Transform-based positioning (no DOM layout recalculation)</li>
            <li>✅ Direct style manipulation (bypasses Alpine.js reactivity)</li>
            <li>✅ Hardware acceleration with GPU layers</li>
            <li>✅ CSS containment for isolated rendering</li>
            <li>✅ Disabled transitions during interaction</li>
            <li>✅ Pointer events optimization</li>
        </ul>
    </div>
    
    <div>
        <button class="btn btn-primary" @click="openModal()">Open High-Performance Modal</button>
        <button class="btn btn-primary" @click="addHeavyContent()">Add Heavy Content</button>
    </div>
    
    <div>
        <p>FPS Counter: <span x-text="fps"></span></p>
        <p>Modal Position: <span x-text="`${modalX}, ${modalY}`"></span></p>
        <p>Modal Size: <span x-text="`${modalWidth}x${modalHeight}`"></span></p>
    </div>

    <!-- Modal -->
    <div x-show="showModal" class="modal-overlay" @click.away="showModal = false">
        <div id="modal" 
             class="modal-resizable bg-white rounded-lg shadow-xl fixed top-0 left-0"
             :style="`width: ${modalWidth}px; height: ${modalHeight}px; transform: translate(${modalX}px, ${modalY}px);`">
            
            <!-- Header with drag handle -->
            <div class="p-4 border-b bg-gray-50 cursor-move rounded-t-lg"
                 @mousedown="startDrag($event)">
                <div class="flex justify-between items-center">
                    <h2 class="text-lg font-bold">High-Performance Modal</h2>
                    <button @click="showModal = false" class="text-gray-500 hover:text-gray-700">×</button>
                </div>
            </div>
            
            <!-- Content -->
            <div class="p-4 overflow-auto" :style="`height: ${modalHeight - 60}px;`">
                <div x-html="content"></div>
            </div>
            
            <!-- Resize handles -->
            <div class="resize-handle resize-se" @mousedown="startResize($event, 'se')"></div>
            <div class="resize-handle resize-sw" @mousedown="startResize($event, 'sw')"></div>
            <div class="resize-handle resize-ne" @mousedown="startResize($event, 'ne')"></div>
            <div class="resize-handle resize-nw" @mousedown="startResize($event, 'nw')"></div>
            <div class="resize-handle resize-n" @mousedown="startResize($event, 'n')"></div>
            <div class="resize-handle resize-s" @mousedown="startResize($event, 's')"></div>
            <div class="resize-handle resize-w" @mousedown="startResize($event, 'w')"></div>
            <div class="resize-handle resize-e" @mousedown="startResize($event, 'e')"></div>
        </div>
    </div>

    <script>
        function performanceTest() {
            return {
                showModal: false,
                modalX: 100,
                modalY: 100,
                modalWidth: 600,
                modalHeight: 400,
                isDragging: false,
                isResizing: false,
                fps: 0,
                content: '<h3>Basic Content</h3><p>Try dragging the header or resizing from the corners/edges.</p><p>This should be buttery smooth!</p>',

                init() {
                    this.startFPSCounter();
                },

                startFPSCounter() {
                    let lastTime = performance.now();
                    let frameCount = 0;
                    
                    const updateFPS = () => {
                        frameCount++;
                        const currentTime = performance.now();
                        
                        if (currentTime - lastTime >= 1000) {
                            this.fps = Math.round(frameCount * 1000 / (currentTime - lastTime));
                            frameCount = 0;
                            lastTime = currentTime;
                        }
                        
                        requestAnimationFrame(updateFPS);
                    };
                    
                    requestAnimationFrame(updateFPS);
                },

                openModal() {
                    this.showModal = true;
                },

                addHeavyContent() {
                    let heavyContent = '<h3>Heavy Content Test</h3>';
                    for (let i = 0; i < 100; i++) {
                        heavyContent += `<div style="padding: 10px; border: 1px solid #ddd; margin: 5px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0);">Item ${i + 1} - This is heavy content to test performance during drag/resize operations.</div>`;
                    }
                    this.content = heavyContent;
                    this.showModal = true;
                },

                startDrag(event) {
                    event.preventDefault();
                    this.isDragging = true;
                    
                    const startX = this.modalX;
                    const startY = this.modalY;
                    const dragStartX = event.clientX;
                    const dragStartY = event.clientY;
                    
                    const modalElement = document.getElementById('modal');
                    modalElement.classList.add('dragging');
                    
                    const handleMouseMove = (e) => {
                        if (!this.isDragging) return;
                        
                        const newX = Math.max(0, Math.min(startX + (e.clientX - dragStartX), window.innerWidth - this.modalWidth));
                        const newY = Math.max(0, Math.min(startY + (e.clientY - dragStartY), window.innerHeight - this.modalHeight));
                        
                        modalElement.style.transform = `translate(${newX}px, ${newY}px)`;
                    };
                    
                    const handleMouseUp = () => {
                        this.isDragging = false;
                        modalElement.classList.remove('dragging');
                        
                        const transform = modalElement.style.transform;
                        const matches = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/);
                        if (matches) {
                            this.modalX = parseFloat(matches[1]);
                            this.modalY = parseFloat(matches[2]);
                        }
                        
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                    };
                    
                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                },

                startResize(event, direction) {
                    event.preventDefault();
                    event.stopPropagation();
                    this.isResizing = true;
                    
                    const startWidth = this.modalWidth;
                    const startHeight = this.modalHeight;
                    const startX = this.modalX;
                    const startY = this.modalY;
                    const resizeStartX = event.clientX;
                    const resizeStartY = event.clientY;
                    
                    const modalElement = document.getElementById('modal');
                    modalElement.classList.add('resizing');
                    
                    const handleMouseMove = (e) => {
                        if (!this.isResizing) return;
                        
                        const deltaX = e.clientX - resizeStartX;
                        const deltaY = e.clientY - resizeStartY;
                        
                        let newWidth = startWidth;
                        let newHeight = startHeight;
                        let newX = startX;
                        let newY = startY;
                        
                        if (direction.includes('e')) newWidth = Math.max(300, startWidth + deltaX);
                        if (direction.includes('w')) {
                            const proposedWidth = startWidth - deltaX;
                            if (proposedWidth >= 300) {
                                newWidth = proposedWidth;
                                newX = startX + deltaX;
                            }
                        }
                        if (direction.includes('s')) newHeight = Math.max(200, startHeight + deltaY);
                        if (direction.includes('n')) {
                            const proposedHeight = startHeight - deltaY;
                            if (proposedHeight >= 200) {
                                newHeight = proposedHeight;
                                newY = startY + deltaY;
                            }
                        }
                        
                        const maxX = window.innerWidth - newWidth;
                        const maxY = window.innerHeight - newHeight;
                        newX = Math.max(0, Math.min(newX, maxX));
                        newY = Math.max(0, Math.min(newY, maxY));
                        
                        modalElement.style.width = newWidth + 'px';
                        modalElement.style.height = newHeight + 'px';
                        modalElement.style.transform = `translate(${newX}px, ${newY}px)`;
                    };
                    
                    const handleMouseUp = () => {
                        this.isResizing = false;
                        modalElement.classList.remove('resizing');
                        
                        const computedStyle = window.getComputedStyle(modalElement);
                        this.modalWidth = parseInt(computedStyle.width);
                        this.modalHeight = parseInt(computedStyle.height);
                        
                        const transform = modalElement.style.transform;
                        const matches = transform.match(/translate\(([^,]+)px,\s*([^)]+)px\)/);
                        if (matches) {
                            this.modalX = parseFloat(matches[1]);
                            this.modalY = parseFloat(matches[2]);
                        }
                        
                        document.removeEventListener('mousemove', handleMouseMove);
                        document.removeEventListener('mouseup', handleMouseUp);
                    };
                    
                    document.addEventListener('mousemove', handleMouseMove);
                    document.addEventListener('mouseup', handleMouseUp);
                }
            }
        }
    </script>
</body>
</html>
