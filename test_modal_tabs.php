<?php
require_once 'system/bootstrap.php';
use edge\Edge;

// Simple test page to demonstrate the new modal tab functionality
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Tabs Test</title>
    <x-layout-head />
</head>
<body class="h-full bg-gray-100" x-data="modalManager()" x-bind:class="showModal && 'overflow-hidden'">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">Modal Tabs & Drag/Resize Test</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Modal Features</h2>
            <p class="text-gray-600 mb-6">Click the buttons below to test the new modal functionality:</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <!-- Open Legacy Modal (single content) -->
                <button type="button"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="<?= APP_ROOT ?>/api/modal/example_tab_content"
                        hx-target="#modal_body"
                        hx-vals='{"title": "Legacy Modal", "content": "This is legacy single-content modal"}'
                        @click="showModal = true">
                    Open Legacy Modal
                </button>

                <!-- Open First Tab -->
                <button type="button"
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="<?= APP_ROOT ?>/api/modal/example_tab_content"
                        hx-target="#modal_body"
                        hx-vals='{"title": "First Tab", "id": "tab1"}'
                        @click="showModal = true">
                    Open First Tab
                </button>

                <!-- Open Second Tab -->
                <button type="button"
                        class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="<?= APP_ROOT ?>/api/modal/example_tab_content"
                        hx-target="#modal_body"
                        hx-vals='{"title": "Second Tab", "id": "tab2", "content": "<div class=\"p-4\"><h3 class=\"text-lg font-bold mb-2\">Second Tab Content</h3><p>This is the content for the second tab!</p><button type=\"button\" class=\"mt-4 bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\" hx-post=\"<?= APP_ROOT ?>/api/modal/example_tab_content\" hx-target=\"#modal_body\" hx-vals='{\"title\": \"Third Tab\", \"id\": \"tab3\", \"content\": \"<div class=\\\"p-4\\\"><h3 class=\\\"text-lg font-bold\\\">Third Tab</h3><p>Opened from within another tab!</p></div>\"}'>Open Third Tab</button></div>"}'
                        @click="showModal = true">
                    Open Second Tab
                </button>

                <!-- Test Quote View in Tab -->
                <button type="button"
                        class="bg-orange-500 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="<?= APP_ROOT ?>/api/quotes/view"
                        hx-target="#modal_body"
                        hx-vals='{"quote_number": "Q-2024-001", "open_in_tab": true, "tab_title": "Quote Details"}'
                        @click="showModal = true">
                    Open Quote in Tab
                </button>

                <!-- Toggle Resize Mode -->
                <button type="button"
                        class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                        @click="toggleResize(); if (!showModal) showModal = true">
                    <span x-text="modalResizable ? 'Disable Resize' : 'Enable Resize'"></span>
                </button>

                <!-- Show Modal Info -->
                <button type="button"
                        class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded"
                        @click="alert(`Modal State:\nTabs: ${modalTabs.length}\nActive: ${activeTabIndex}\nResizable: ${modalResizable}\nPosition: ${modalX}, ${modalY}\nSize: ${modalWidth}x${modalHeight}`)">
                    Show Modal State
                </button>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <h2 class="text-xl font-semibold mb-4">Instructions</h2>
            <div class="space-y-4 text-gray-700">
                <div>
                    <h3 class="font-semibold">Tab Features:</h3>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>Click "Open First Tab" or "Open Second Tab" to create tabs</li>
                        <li>Multiple tabs will show a tab navigation bar</li>
                        <li>Click tab titles to switch between tabs</li>
                        <li>Click the × button to close individual tabs (when multiple tabs exist)</li>
                        <li>Content from within tabs can open new tabs</li>
                    </ul>
                </div>
                
                <div>
                    <h3 class="font-semibold">Drag & Resize Features:</h3>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>Click "Enable Resize" to make the modal draggable and resizable</li>
                        <li>Drag the modal header to move the modal around</li>
                        <li>Use the resize handles on corners and edges to resize</li>
                        <li>The modal will stay within the browser window bounds</li>
                        <li>Click "Disable Resize" to return to normal centered modal</li>
                    </ul>
                </div>

                <div>
                    <h3 class="font-semibold">Server Integration:</h3>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>Server responses can include <code>HX-Open-Tab</code> header to open content in tabs</li>
                        <li>Existing API endpoints can be modified to support tab opening</li>
                        <li>Tab content is loaded via HTMX and can include interactive elements</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the modal component -->
    <x-component-modal />
</body>
</html>
